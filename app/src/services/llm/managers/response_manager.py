"""Response management functionality for LLM service."""

from typing import Any, AsyncGenerator, Callable, List, Optional

from app.src.exceptions.error_code import <PERSON><PERSON><PERSON><PERSON><PERSON>rCode
from app.src.services.prompt_service import PromptService
from app.src.schemas.chat_sessions import Cha<PERSON><PERSON><PERSON>ory<PERSON><PERSON>

from ..core.llm_client import LLMClient
from ..core.stream_processor import StreamProcessor
from ..core.error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .tool_manager import ToolManager
from .message_manager import MessageManager


class ResponseManager:
    """Manages response generation and streaming for LLM operations."""

    def __init__(
        self,
        llm_client: LLMClient,
        tool_manager: Too<PERSON>Manager,
        message_manager: MessageManager,
        prompt_service: Optional[PromptService] = None,
    ):
        """
        Initialize the response manager.
        
        Args:
            llm_client: LLM client for API interactions
            tool_manager: Tool manager for tool operations
            message_manager: Message manager for message preparation
            prompt_service: Prompt service for system prompts
        """
        self.llm_client = llm_client
        self.tool_manager = tool_manager
        self.message_manager = message_manager
        self.prompt_service = prompt_service
        
        # Initialize error handler and stream processor
        self.error_handler = ErrorHandler(self.llm_client)
        self.stream_processor = StreamProcessor(
            llm_client=self.llm_client,
            tool_registry=self.tool_manager.get_tool_registry(),
            error_handler=self.error_handler,
        )

    async def generate_stream_response(
        self,
        request: Any,
        chat_history: List[ChatHistoryItem],
        max_tokens: int = 4096,
        temperature: float = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response for the given request and chat history.

        Args:
            request: User request object
            chat_history: List of previous chat messages
            max_tokens: Maximum tokens in response
            temperature: Temperature parameter for randomness
            on_tool_call: Optional callback for tool call notifications

        Yields:
            Streaming response content

        Raises:
            BusinessException: For various API errors (via ChatbotErrorCode)
        """
        try:
            # Get system prompt
            system_prompt = await self._get_system_prompt()
        except ValueError:
            # If prompt service fails, raise appropriate error
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value

        # Prepare messages using message manager
        messages = self.message_manager.prepare_messages(
            system_prompt=system_prompt,
            chat_history=chat_history,
            current_request=request,
        )

        try:
            # Process stream with tool handling
            async for chunk in self.stream_processor.process_stream(
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                on_tool_call=on_tool_call,
            ):
                yield chunk

        except Exception:
            # Handle any errors that weren't caught by the stream processor
            # Stream error response instead of raising exception
            error_message = "❌ 処理中にエラーが発生しました。もう一度お試しください。"
            async for chunk in self.error_handler.stream_error_response(error_message):
                yield chunk

    async def _get_system_prompt(self) -> str:
        """
        Get system prompt from prompt service.
        
        Returns:
            System prompt string
            
        Raises:
            ValueError: If prompt service is not available or fails
        """
        if not self.prompt_service:
            raise ValueError("Prompt service is not available")
        
        return await self.prompt_service._get_prompts()

    def get_response_metadata(self) -> dict:
        """
        Get metadata about response capabilities.
        
        Returns:
            Dictionary containing response metadata
        """
        return {
            "available_tools": self.tool_manager.get_available_tools(),
            "tool_configs": self.tool_manager.get_tool_configs(),
            "supports_streaming": True,
            "supports_tool_calls": True,
        }

    async def validate_request(self, request: Any) -> bool:
        """
        Validate incoming request for response generation.
        
        Args:
            request: User request object to validate
            
        Returns:
            True if request is valid, False otherwise
        """
        # Basic validation - check if request has required fields
        if not hasattr(request, 'question') or not request.question:
            return False
        
        # Additional validation can be added here
        return True
