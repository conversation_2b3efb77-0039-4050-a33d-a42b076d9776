"""
Excel file creation tool for LLM function calling.

This tool creates downloadable Excel files for advertising campaigns.
"""

import json
from pathlib import Path
from typing import Any, Dict

from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils.prompt_generator import PromptGenerator
from app.src.services.prompt_service import PromptService
from .base_tool import BaseTool


class ExcelTool(BaseTool):
    """
    Tool for creating Excel files for advertising campaigns.
    
    This tool handles the complete workflow of Excel file generation
    including data extraction, formatting, and file creation.
    """
    
    def __init__(self, prompt_service: PromptService, llm_client):
        """
        Initialize the Excel tool.
        
        Args:
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for data extraction
        """
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()
    
    @property
    def name(self) -> str:
        """Return the tool name."""
        return "create_excel_file"
    
    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Creates a downloadable Excel file for an advertising campaign based on the specified platform."
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "platform": {
                    "type": "string",
                    "description": (
                        "The advertising platform (e.g., LINE Ads, Meta, YouTube Ads, "
                        "Google Search Ads, Google Display Ads, Google Demand Gen Ads, P-Max)."
                    ),
                    "enum": [
                        "LINE Ads",
                        "Meta (Instagram/Facebook)",
                        "YouTube Ads",
                        "Google Search Ads",
                        "Google Display Ads",
                        "Google Demand Gen Ads",
                        "P-Max"
                    ]
                }
            },
            "required": ["platform"]
        }
    
    async def execute(self, platform: str, **kwargs) -> Dict[str, Any]:
        """
        Execute Excel file creation.

        Args:
            platform: Advertising platform name
            **kwargs: Additional parameters including conversation context

        Returns:
            Dictionary with download URL
        """
        try:
            # Extract conversation context for data extraction
            conversation_context = kwargs.get('conversation_context', {})
            messages = conversation_context.get('messages', [])

            # Extract campaign data from conversation
            campaign_data = await self._extract_campaign_data(platform, messages)

            # Create Excel file
            download_url = await self.excel_manager.create_excel_file(platform, campaign_data)

            return {"download_url": download_url}

        except ValueError as e:
            raise Exception(str(e))
        except Exception as e:
            raise Exception(f"Excel file creation failed: {str(e)}")

    async def _extract_campaign_data(self, platform: str, messages: list) -> Dict[str, Any]:
        """
        Extract campaign data from conversation messages using LLM.

        Args:
            platform: Advertising platform
            messages: Conversation messages

        Returns:
            Extracted campaign data
        """
        try:
            # Load output format for the platform
            format_file = self.excel_manager.platform_mapping.get(platform)
            if not format_file:
                raise ValueError(f"No format file found for platform: {platform}")

            output_format = await self.excel_manager._load_output_format(format_file)
            schema_fields = output_format.get("fields", {})

            # Generate extraction prompt
            schema_prompt = self.prompt_generator.generate_prompt_from_output_format(
                platform=platform,
                fields=schema_fields
            )

            # Get system prompt and enhance it
            system_prompt = await self.prompt_service._get_prompts()
            enhanced_prompt = (
                f"{system_prompt}\n\n{schema_prompt}\n"
                "Extract all relevant data from the conversation history to populate the format completely and logically, "
                "maximizing campaign effectiveness. Ensure all values are full, persuasive Japanese ad copy based on product context. "
                "Strictly return only a valid JSON object using this exact format:\n\n"
                '{"data": { ... }, "missing_fields": [ ... ]}\n\n'
                "Do not include extra commentary, explanations, or markdown formatting. "
                "If data is missing, leave fields blank but maintain structure."
            )

            # Prepare messages for extraction
            extraction_messages = [{"role": "system", "content": enhanced_prompt}]

            # Add conversation history (exclude system prompt and current message)
            if messages:
                extraction_messages.extend(messages[1:-1])

            # Extract data using LLM
            response = await self.llm_client.create_chat_completion(
                messages=extraction_messages,
                max_tokens=4096,
                temperature=0
            )

            # Parse the response
            try:
                campaign_data = json.loads(response.choices[0].message.content)
                return campaign_data
            except json.JSONDecodeError:
                # Fallback to default data if parsing fails
                return self._get_default_campaign_data(platform)

        except Exception as e:
            # Fallback to default data if extraction fails
            print(f"Campaign data extraction failed: {e}")
            return self._get_default_campaign_data(platform)

    def _get_default_campaign_data(self, platform: str) -> Dict[str, Any]:
        """
        Get default campaign data when extraction fails.

        Args:
            platform: Advertising platform

        Returns:
            Default campaign data structure
        """
        return {
            "data": {
                "キャンペーン名": f"{platform}キャンペーン",
                "広告グループ名": ["グループ1", "グループ2"],
                "配信条件": ["条件1", "条件2"],
                "性別": "すべて",
                "年齢": "18-65",
                "エリア": "日本全国",
                "見出し": [f"{platform}の魅力的な見出し", "効果的な広告文"],
                "説明文": [f"{platform}向けの説明文", "詳細な商品説明"],
            },
            "missing_fields": []
        }
    

