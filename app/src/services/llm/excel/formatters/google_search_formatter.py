"""
Google Search Ads Excel formatter.

This module handles Excel file generation for Google Search advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class GoogleSearchFormatter(BaseExcelWriter):
    """
    Excel formatter for Google Search advertising campaigns.
    """
    
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for Google Search advertising campaigns.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("検索広告フォーマット")
        
        # This is a simplified implementation
        # The full implementation would follow the same pattern as the original
        # but with better structure and separation of concerns
        
        data = campaign_data.get('data', {})
        
        # Headers for main table
        table1_headers = [
            "媒体", "キャンペーン", "広告グループ", "メインキーワード", 
            "掛合わせ1", "マッチタイプ", "広告"
        ]
        
        for idx, header in enumerate(table1_headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        
        # Platform and campaign (non-list fields)
        platform = self.get_field_value(data.get("媒体", "Google"))
        set_cell(ws, 2, 1, platform, merge_end_row=3)
        
        campaign = self.get_field_value(data.get("キャンペーン", ""))
        set_cell(ws, 2, 2, campaign, merge_end_row=3)
        
        # List fields
        list_fields = ["広告グループ", "メインキーワード", "掛合わせ1", "マッチタイプ", "広告"]
        max_items = 2
        
        for idx, header in enumerate(table1_headers[2:], start=3):
            values = self.get_field_value(data.get(header, []))
            values = self.ensure_list(values, max_items=max_items)
            for row, value in enumerate(values, start=2):
                if row <= 3:  # Limit to 2 rows
                    set_cell(ws, row, idx, value)
        
        # Ad details table (simplified)
        table2_headers = [
            "広告名", "見出し(30字以内)", "固定", "文字数", 
            "説明文(90字以内)", "固定", "文字数", "パス", "文字数"
        ]
        
        for idx, header in enumerate(table2_headers, start=10):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        
        # Headlines
        headlines = self.get_field_value(data.get("見出し", []))
        headlines = self.ensure_list(headlines, max_items=15)
        for idx, headline in enumerate(headlines):
            row = 2 + idx
            set_cell(ws, row, 11, headline)
            set_cell(ws, row, 13, str(len(headline)))
        
        # Descriptions
        descriptions = self.get_field_value(data.get("説明文", []))
        descriptions = self.ensure_list(descriptions, max_items=4)
        for idx, description in enumerate(descriptions):
            row = 2 + idx
            set_cell(ws, row, 14, description)
            set_cell(ws, row, 16, str(len(description)))
        
        # Paths
        paths = self.get_field_value(data.get("パス", []))
        paths = self.ensure_list(paths, max_items=2)
        for idx, path in enumerate(paths):
            row = 2 + idx
            set_cell(ws, row, 17, path)
            set_cell(ws, row, 18, str(len(path)))
        
        self.save_workbook(wb, file_path)
