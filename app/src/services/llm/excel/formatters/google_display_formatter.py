"""
Google Display Ads Excel formatter.

This module handles Excel file generation for Google Display advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class GoogleDisplayFormatter(BaseExcelWriter):
    """
    Excel formatter for Google Display advertising campaigns.
    """
    
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for Google Display advertising campaigns.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("Googleディスプレイ広告")
        
        # Headers
        headers = [
            "媒体", "キャンペーン名", "広告グループ名", "配信条件", 
            "デバイス", "性別", "年齢", "エリア", "除外プレースメント"
        ]
        
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        
        data = campaign_data.get('data', {})

        # Non-list fields
        non_list_fields = [
            "媒体", "キャンペーン名", "デバイス", "性別",
            "年齢", "エリア", "除外プレースメント"
        ]

        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = self.get_field_value(data.get(header, ""))
                set_cell(ws, 2, idx, value)

        # List fields
        list_fields = {"広告グループ名": 2, "配信条件": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = self.get_field_value(data.get(header, []))
                values = self.ensure_list(values, max_items=2)
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)

        # Ad details section
        self._write_ad_details(ws, data)

        self.save_workbook(wb, file_path)

    def _write_ad_details(self, ws, data: Dict[str, Any]) -> None:
        """Write ad details section."""
        # Ad text headers
        ad_headers = [
            "広告名", "見出し(30字以内)", "固定", "文字数",
            "説明文(90字以内)", "固定", "文字数", "パス", "文字数"
        ]

        for idx, header in enumerate(ad_headers, start=10):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)

        # Headlines
        headlines = self.get_field_value(data.get("見出し", []))
        headlines = self.ensure_list(headlines, max_items=15)
        for idx, headline in enumerate(headlines):
            row = 2 + idx
            set_cell(ws, row, 11, headline)
            set_cell(ws, row, 13, str(len(headline)))

        # Descriptions
        descriptions = self.get_field_value(data.get("説明文", []))
        descriptions = self.ensure_list(descriptions, max_items=4)
        for idx, description in enumerate(descriptions):
            row = 2 + idx
            set_cell(ws, row, 14, description)
            set_cell(ws, row, 16, str(len(description)))

        # Paths
        paths = self.get_field_value(data.get("パス", []))
        paths = self.ensure_list(paths, max_items=2)
        for idx, path in enumerate(paths):
            row = 2 + idx
            set_cell(ws, row, 17, path)
            set_cell(ws, row, 18, str(len(path)))
