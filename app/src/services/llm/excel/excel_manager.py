"""
Excel manager for coordinating Excel file generation.

This module manages the creation of Excel files for different advertising platforms.
"""

import json
import os
from pathlib import Path
from tempfile import gettempdir
from typing import Any, Dict
from uuid import uuid4

import aiofiles

from .formatters.meta_formatter import MetaFormatter
from .formatters.line_formatter import LineFormatter
from .formatters.youtube_formatter import YouTubeFormatter
from .formatters.google_search_formatter import GoogleSearchFormatter
from .formatters.google_display_formatter import GoogleDisplayFormatter
from .formatters.google_demand_gen_formatter import GoogleDemandGenFormatter
from .formatters.pmax_formatter import PMaxFormatter


class ExcelManager:
    """
    Manages Excel file generation for different advertising platforms.
    """
    
    def __init__(self, outputs_dir: Path):
        """
        Initialize the Excel manager.
        
        Args:
            outputs_dir: Directory containing output format files
        """
        self.outputs_dir = outputs_dir
        self.formatters = {
            "Meta (Instagram/Facebook)": MetaFormatt<PERSON>(),
            "LINE Ads": <PERSON><PERSON><PERSON><PERSON><PERSON>(),
            "YouTube Ads": <PERSON><PERSON><PERSON><PERSON><PERSON>(),
            "Google Search Ads": GoogleSearchFormatter(),
            "Google Display Ads": GoogleDisplayFormatter(),
            "Google Demand Gen Ads": GoogleDemandGenFormatter(),
            "P-Max": PMaxFormatter(),
        }
        self.platform_mapping = {
            "LINE Ads": "line_ads_format.json",
            "Meta (Instagram/Facebook)": "meta_ads_format.json",
            "YouTube Ads": "youtube_ads_format.json",
            "Google Search Ads": "google_search_ads_format.json",
            "Google Display Ads": "google_display_ads_format.json",
            "Google Demand Gen Ads": "google_demand_gen_ads_format.json",
            "P-Max": "p_max_format.json"
        }
    
    async def create_excel_file(self, platform: str, campaign_data: Dict[str, Any]) -> str:
        """
        Create an Excel file for the specified platform.
        
        Args:
            platform: Advertising platform name
            campaign_data: Campaign data dictionary
            
        Returns:
            Download URL for the created file
            
        Raises:
            ValueError: If platform is not supported
        """
        if platform not in self.formatters:
            supported_platforms = ", ".join(self.formatters.keys())
            raise ValueError(
                f"指定されたプラットフォーム「{platform}」は対応していません。"
                f"以下の中から選択してください：{supported_platforms}"
            )
        
        # Load output format
        format_file = self.platform_mapping.get(platform)
        if not format_file:
            raise ValueError(f"No format file found for platform: {platform}")
        
        output_format = await self._load_output_format(format_file)
        
        # Generate file
        filename = f"gen_excel_{uuid4().hex}.xlsx"
        file_path = os.path.join(gettempdir(), filename)
        
        formatter = self.formatters[platform]
        await formatter.write_excel_file(campaign_data, output_format, file_path)
        
        download_url = f"/be/chat/download-excel?filename={filename}"
        return download_url
    
    async def _load_output_format(self, format_file: str) -> Dict[str, Any]:
        """
        Load output format from JSON file.
        
        Args:
            format_file: Format file name
            
        Returns:
            Output format dictionary
        """
        file_path = self.outputs_dir / format_file
        async with aiofiles.open(file_path, mode='r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
